{
  "Logging": {
    "LogLevel": {
      "Default": "Trace",
      "Avanade": "Trace",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  },
  "OpenAI": {
    "ApiKey": "********************************",
    "Endpoint": "https://oai-attane-dev-proto-001.openai.azure.com",
    "DeploymentName": "gpt-4o-mini"
  },
  "AISearch": {
    "ServiceName": "srch-attane-dev-proto-002",
    "ApiKey": "DIpUEiPc2cBU8jyV91aIp2wXdKs3t8vBqdyselzyLlAzSeBog6wX",
    "Mail": [
      {
        "MailIndex": "srch-attane-mail-x2-index",
        "MailSemanticConfiguration": "srch-attane-mail4-semanticconf"
      }
    ],
    "SPO": [
      {
        "SPOIndex": "spo15-index",
        "SPOSemanticConfiguration": "spo15-index-semantic-configuration"
      }
    ],
    "Chat": [
      {
        "ChatIndex": "teamschat2-index",
        "ChatSemanticConfiguration": "teamschat-index-semantic-configuration"
        // "ChatIndex": "teamschats1-index",
        // "ChatSemanticConfiguration": "teamschats1-index-semantic-configuration"
      }
    ]
  },
  "ApplicationInsights": {
    "ConnectionString": "InstrumentationKey=a7736d1b-4449-4690-957b-f17ef02a01b4;IngestionEndpoint=https://japaneast-1.in.applicationinsights.azure.com/;LiveEndpoint=https://japaneast.livediagnostics.monitor.azure.com/"
  },
  "StorageAccount": {
    "ConnectionString": "DefaultEndpointsProtocol=https;AccountName=stattanedevmec001;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net",
    "ConnectionStringForTeamsChats": "DefaultEndpointsProtocol=https;AccountName=stattanedevmec002;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net"
  },
  "ServiceBus": {
    "ConnectionString": "Endpoint=sb://sb-attane-dev-001.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=2PlIX04L8G5zoP7m4TxUlhdTGPDQOI+PW+ASbCqTMFk="
  },
  "AzureAD": {
    "Audience": "api://localhost:3000/6b399905-b6c1-4abe-a15c-e694024d9907",
    "TenantId": "52fc0f11-0c64-40d6-b802-3b80cbef8e3f",
    "ClientId": "6b399905-b6c1-4abe-a15c-e694024d9907",
    "ClientSecret": "****************************************",
    "Scopes": {
      "Graph": "https://graph.microsoft.com/.default",
      "Spo": "https://ydxxq.sharepoint.com/.default",
      "Api": "api://localhost:3000/6b399905-b6c1-4abe-a15c-e694024d9907/user_impersonation"
    },
    "AllowAppIds": [
      "07f0a107-95c1-41ad-8f13-912eab68b93f",
      "1fec8e78-bce4-4aaf-ab1b-5451cc387264",
      "5e3ce6c0-2b1f-4285-8d4b-75ee78787346",
      "469c477c-b4cb-42e0-a90c-170f22e2b217"
    ]
  },
  "Geranium": {
    "Search": {
      "TokenKeys": [
        "Spo",
        "Graph"
      ],
      "QueueName": "search-process-dev-mec-001",
      "DataSources": [
        {
          "kind": "Chat",
          "globalProperties": {},
          "properties": [
            {}
          ]
        },
        {
          "kind": "Mail",
          "globalProperties": {},
          "properties": [
            {}
          ]
        },
        {
          "kind": "SPO",
          "globalProperties": {
            "site": "https://ydxxq.sharepoint.com/sites/mitene-dev/"
          },
          "properties": [
            {
              "site": "https://ydxxq.sharepoint.com/sites/mitene-dev/",
              "list": "710ddfb8-142c-4dd9-b9b0-54aa24e9da7b",
              "listUrl": "https://ydxxq.sharepoint.com/sites/mitene-dev/Lists/List/DispForm.aspx",
              "listName": "会社からのお知らせ",
              "category": "category1",
              "filterByPresentPeriod": "true"
            }
          ]
        },
        {
          "kind": "SPO",
          "properties": [
            {
              "site": "https://ydxxq.sharepoint.com/sites/mitene-dev/",
              "list": "d2a49bb4-161e-48ce-8a69-2c071ef4da7b",
              "listUrl": "https://ydxxq.sharepoint.com/sites/mitene-dev/Lists/List1/DispForm.aspx",
              "listName": "テスト",
              "category": "category1",
              "filterByPresentPeriod": "true"
            }
          ]
        },
        {
          "kind": "SPO",
          "properties": [
            {
              "site": "https://ydxxq.sharepoint.com/sites/mitene-dev/",
              "list": "e14a00d3-04f6-46a1-9dbf-46898f56601e",
              "listUrl": "https://ydxxq.sharepoint.com/sites/mitene-dev/Lists/List3/DispForm.aspx",
              "listName": "テスト２",
              "category": "category1",
              "filterByPresentPeriod": "true"
            }
          ]
        },
        {
          "kind": "SPO",
          "properties": [
            {
              "site": "https://ydxxq.sharepoint.com/sites/atTane-dev/",
              "list": "b5f276f0-5ba7-4051-93e8-cf55abef02ed",
              "listUrl": "https://ydxxq.sharepoint.com/sites/atTane-dev/Lists/atTaneSPO/DispForm.aspx",
              "listName": "atTane用SPOサイト",
              "category": "category1",
              "filterByPresentPeriod": "false",
              "timezoneOffset": "+09:00"
            }
          ]
        },
        {
          "kind": "SPO",
          "properties": [
            {
              "site": "https://ydxxq.sharepoint.com/sites/atTane-dev/",
              "list": "dc546108-8fb0-41ce-b7f8-94b469179576",
              "listUrl": "https://ydxxq.sharepoint.com/sites/atTane-dev/Lists/List1/DispForm.aspx",
              "listName": "期限なしリスト",
              "category": "category1"
            }
          ]
        }
      ]
    }
  }
}